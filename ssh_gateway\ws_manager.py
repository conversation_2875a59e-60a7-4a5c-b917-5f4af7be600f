# ssh_gateway/ws_manager.py

import asyncio
from fastapi import WebSocket
import logging
from typing import Dict, Tuple

logging.basicConfig(level=logging.INFO)

class ConnectionManager:
    """
    单例模式的连接管理器，负责：
    1. 维护 device_id 到 WebSocket 的映射。
    2. 协调 SSH 会话和 WebSocket 之间的数据流。
    """
    def __init__(self):
        # 存储活动的WebSocket连接: {device_id: WebSocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储等待结果的Future对象: {device_id: asyncio.Future}
        self.result_futures: Dict[str, asyncio.Future] = {}
        logging.info("ConnectionManager initialized.")

    async def connect(self, device_id: str, websocket: WebSocket):
        """接受新的WebSocket连接并存储。"""
        await websocket.accept()
        self.active_connections[device_id] = websocket
        logging.info(f"Device '{device_id}' connected via WebSocket.")

    def disconnect(self, device_id: str):
        """断开并清理指定device_id的连接。"""
        if device_id in self.active_connections:
            del self.active_connections[device_id]
        if device_id in self.result_futures:
            # 如果有正在等待的任务，让它异常退出
            future = self.result_futures.pop(device_id)
            future.set_exception(ConnectionError(f"Device '{device_id}' disconnected."))
        logging.info(f"Device '{device_id}' disconnected.")

    async def send_command(self, device_id: str, command_data: str) -> str:
        """
        通过WebSocket将命令发送给小程序，并异步等待结果。
        
        优化点：此函数现在处理原始数据流，而不仅仅是换行符分割的命令。
        """
        websocket = self.active_connections.get(device_id)
        if not websocket:
            logging.error(f"Cannot send command: No active WebSocket for device '{device_id}'.")
            raise ConnectionError(f"Device '{device_id}' is not connected.")

        # 为本次命令创建一个新的Future来等待结果
        future = asyncio.get_event_loop().create_future()
        self.result_futures[device_id] = future

        try:
            # 构造并发送JSON指令
            await websocket.send_json({"type": "input", "data": command_data})
            
            # 异步等待结果，设置一个超时时间防止无限等待
            result = await asyncio.wait_for(future, timeout=30.0)
            return result
        except asyncio.TimeoutError:
            logging.error(f"Timeout waiting for result from device '{device_id}'.")
            raise
        finally:
            # 清理本次命令的Future
            if device_id in self.result_futures and self.result_futures[device_id] is future:
                del self.result_futures[device_id]

    def on_result_received(self, device_id: str, result_data: str):
        """
        当从WebSocket收到结果时，调用此方法来完成对应的Future。
        """
        future = self.result_futures.get(device_id)
        if future and not future.done():
            future.set_result(result_data)
        else:
            logging.warning(f"Received result for '{device_id}', but no pending future found.")

# 创建一个全局唯一的管理器实例
manager = ConnectionManager()
