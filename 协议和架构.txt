ws之间的通讯协议
type: 消息类型，output 表示后端终端的标准输出或标准错误。
data: 原始字符流（带 ANSI 转义序列，用于颜色/定位等）。
{
  "type": "input",
  "data": "ls -al\n"
}

{
  "type": "output",
  "data": "total 0\ndrwxr-xr-x  3 <USER>  <GROUP>  96 Jul 17 10:32 .\n..."
}

通讯架构图
flowchart LR
    subgraph "外部网络 (Internet)"
        JMS[("🔐<br>JumpServer<br>(JMS SSH服务)")]
        Server[("🏢<br>公司后台服务器<br>(FastAPI + WebSockets)")]
    end

    subgraph "蓝牙内部网络 (Offline Bluetooth Network)"
        MiniApp[("📱<br>微信小程序<br>(WS Client + BT Client)")]
        subgraph "操作台/工控机 (Linux)"
            direction LR
            BTAgent[("🤖<br>BT-Agent<br>(守护进程)")]
            Shell[("💲<br>Linux Shell")]
        end
    end

    %% SSH指令下发 (蓝色箭头)
    JMS -- "1<br>SSH指令下发<br><b>SSH协议</b>" --> Server
    Server -- "2<br>指令转发<br><b>WebSocket</b>" --> MiniApp
    MiniApp -- "3<br>命令发送<br><b>Bluetooth LE</b>" --> BTAgent
    BTAgent -- "4<br>命令执行<br><b>Unix Domain Socket</b>" --> Shell

    %% 结果回传 (绿色箭头)
    Shell -- "5<br>结果返回<br><b>Unix Domain Socket</b>" --> BTAgent
    BTAgent -- "6<br>结果接收<br><b>Bluetooth LE</b>" --> MiniApp
    MiniApp -- "7<br>结果上报<br><b>WebSocket</b>" --> Server
    Server -- "8<br>SSH结果返回<br><b>SSH协议</b>" --> JMS

    %% 样式定义
    style JMS fill:#e6f7ff,stroke:#91d5ff,stroke-width:2px
    style Server fill:#fffbe6,stroke:#ffe58f,stroke-width:2px
    style MiniApp fill:#f6ffed,stroke:#b7eb8f,stroke-width:2px
    style BTAgent fill:#f0f0f0,stroke:#8c8c8c,stroke-width:2px
    style Shell fill:#f0f0f0,stroke:#8c8c8c,stroke-width:2px
