#!/usr/bin/env python3
"""
简化的SSH连接测试脚本
"""

import subprocess
import sys
import time

def test_ssh_connection():
    """测试SSH连接"""
    
    print("=== SSH连接测试 ===")
    print("测试配置:")
    print("- 主机: **************")
    print("- 端口: 8022")
    print("- 用户: 1234")
    print("- 私钥: keys/client_auth_key")
    print()
    
    # 构建SSH命令
    ssh_cmd = [
        "ssh",
        "-i", "keys/client_auth_key",
        "-p", "8022",
        "-o", "StrictHostKeyChecking=no",  # 忽略主机密钥检查
        "-o", "UserKnownHostsFile=/dev/null",  # 不保存主机密钥
        "-o", "LogLevel=VERBOSE",  # 详细日志
        "1234@**************",
        "echo 'Hello SSH Gateway'"  # 执行简单命令后退出
    ]
    
    print("执行命令:")
    print(" ".join(ssh_cmd))
    print()
    
    try:
        # 执行SSH命令
        result = subprocess.run(
            ssh_cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print("=== 执行结果 ===")
        print(f"返回码: {result.returncode}")
        print(f"标准输出:\n{result.stdout}")
        print(f"标准错误:\n{result.stderr}")
        
        if result.returncode == 0:
            print("✅ SSH连接测试成功！")
            return True
        else:
            print("❌ SSH连接测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ SSH连接超时")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return False

def main():
    """主函数"""
    print("SSH连接问题诊断工具")
    print("=" * 50)
    
    # 检查密钥文件是否存在
    import os
    if not os.path.exists("keys/client_auth_key"):
        print("❌ 私钥文件不存在: keys/client_auth_key")
        return False
    
    if not os.path.exists("keys/client_auth_key.pub"):
        print("❌ 公钥文件不存在: keys/client_auth_key.pub")
        return False
    
    print("✅ 密钥文件检查通过")
    print()
    
    # 显示公钥内容
    try:
        with open("keys/client_auth_key.pub", "r") as f:
            pubkey = f.read().strip()
        print("客户端公钥:")
        print(pubkey)
        print()
    except Exception as e:
        print(f"❌ 读取公钥失败: {e}")
        return False
    
    # 执行连接测试
    success = test_ssh_connection()
    
    if success:
        print("\n🎉 测试完成，连接成功！")
    else:
        print("\n💡 故障排除建议:")
        print("1. 确认SSH服务器正在运行")
        print("2. 检查服务器配置中的公钥列表")
        print("3. 确认网络连接正常")
        print("4. 查看服务器端日志获取详细信息")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        sys.exit(1)
