# main.py

import asyncio
import logging
import uvicorn
import asyncssh
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from ssh_gateway.ws_manager import manager
from ssh_gateway.ssh_server import MySSHServer
from ssh_gateway import config

# --- 日志配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)


# --- FastAPI 应用和 WebSocket 端点 ---
app = FastAPI()

@app.websocket("/ws/{device_id}")
async def websocket_endpoint(websocket: WebSocket, device_id: str):
    """
    WebSocket端点，用于小程序连接。
    """
    try:
        await manager.connect(device_id, websocket)
        while True:
            # 等待小程序发送执行结果
            data = await websocket.receive_json()
            log.info(f"Received from WebSocket ({device_id}): {data}")
            
            # 验证数据格式
            if data.get("type") == "output" and "data" in data:
                manager.on_result_received(device_id, data["data"])
            else:
                log.warning(f"Invalid data format from {device_id}: {data}")

    except WebSocketDisconnect:
        log.info(f"WebSocket for {device_id} disconnected.")
    except Exception as e:
        log.error(f"Error in WebSocket connection for {device_id}: {e}", exc_info=True)
    finally:
        manager.disconnect(device_id)


# --- 服务器启动逻辑 ---
async def start_ssh_server():
    """启动AsyncSSH服务器。"""
    log.info(f"Starting SSH server on port {config.SSH_PORT}...")
    try:
        await asyncssh.create_server(
            MySSHServer,
            host=config.WEB_HOST,
            port=config.SSH_PORT,
            server_host_keys=[config.SSH_HOST_KEY_PATH]
        )
    except Exception as e:
        log.error(f"Failed to start SSH server: {e}", exc_info=True)


async def start_web_server():
    """以编程方式启动Uvicorn服务器，使其与asyncssh在同一个事件循环中。"""
    log.info(f"Starting FastAPI/WebSocket server on http://{config.WEB_HOST}:{config.WEB_PORT}...")
    uv_config = uvicorn.Config(
        app, 
        host=config.WEB_HOST, 
        port=config.WEB_PORT,
        log_level="info"
    )
    server = uvicorn.Server(uv_config)
    # 我们不调用 server.run() 因为它会阻塞。
    # 我们调用 server.serve() 这是一个 awaitable。
    await server.serve()

async def main():
    """主函数，使用asyncio.gather并发运行SSH和Web服务器。"""
    log.info("Gateway starting up...")
    
    # 检查SSH密钥是否存在
    try:
        with open(config.SSH_HOST_KEY_PATH, 'r') as f:
            pass
    except FileNotFoundError:
        log.error("="*50)
        log.error(f"SSH host key not found at '{config.SSH_HOST_KEY_PATH}'!")
        log.error("Please generate it using: ssh-keygen -t rsa -f keys/ssh_host_key")
        log.error("="*50)
        return

    ssh_task = asyncio.create_task(start_ssh_server())
    web_task = asyncio.create_task(start_web_server())

    await asyncio.gather(web_task, ssh_task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log.info("Gateway shutting down.")
