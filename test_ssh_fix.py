#!/usr/bin/env python3
"""
SSH连接测试脚本
用于验证SSH服务器修复后的连接情况
"""

import asyncio
import asyncssh
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_ssh_connection():
    """测试SSH连接"""
    
    # 测试配置
    host = '**************'
    port = 8022
    username = '1234'
    private_key_path = 'keys/client_auth_key'
    
    logger.info(f"开始测试SSH连接到 {host}:{port}")
    
    try:
        # 测试1: 使用私钥认证
        logger.info("=== 测试1: 私钥认证 ===")
        try:
            async with asyncssh.connect(
                host=host,
                port=port,
                username=username,
                client_keys=[private_key_path],
                known_hosts=None,  # 忽略主机密钥验证
                password=None
            ) as conn:
                logger.info("✅ 私钥认证成功！")
                
                # 执行一个简单命令
                result = await conn.run('echo "Hello from SSH client"', check=True)
                logger.info(f"命令执行结果: {result.stdout}")
                
        except asyncssh.PermissionDenied:
            logger.warning("❌ 私钥认证失败")
        except Exception as e:
            logger.error(f"❌ 私钥认证连接错误: {e}")
        
        # 测试2: 密码认证
        logger.info("\n=== 测试2: 密码认证 ===")
        try:
            async with asyncssh.connect(
                host=host,
                port=port,
                username=username,
                password='test123',  # 任意密码，服务器配置为允许任何密码
                known_hosts=None,  # 忽略主机密钥验证
                client_keys=[]  # 不使用私钥
            ) as conn:
                logger.info("✅ 密码认证成功！")
                
                # 执行一个简单命令
                result = await conn.run('echo "Hello from password auth"', check=True)
                logger.info(f"命令执行结果: {result.stdout}")
                
        except asyncssh.PermissionDenied:
            logger.warning("❌ 密码认证失败")
        except Exception as e:
            logger.error(f"❌ 密码认证连接错误: {e}")
            
    except Exception as e:
        logger.error(f"连接测试失败: {e}")
        return False
    
    logger.info("SSH连接测试完成")
    return True

async def main():
    """主函数"""
    logger.info("SSH连接修复验证脚本")
    logger.info("=" * 50)
    
    success = await test_ssh_connection()
    
    if success:
        logger.info("✅ 测试完成")
    else:
        logger.error("❌ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试脚本错误: {e}")
        sys.exit(1)
