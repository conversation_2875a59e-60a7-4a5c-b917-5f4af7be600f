#!/usr/bin/env python3
"""
简单的SSH连接测试 - 只测试连接保持
"""

import asyncio
import asyncssh
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def simple_ssh_test():
    """简单的SSH连接测试"""
    
    host = '**************'
    port = 8022
    username = '1234'
    
    logger.info(f"开始连接到 {host}:{port}")
    
    try:
        # 使用密码认证，因为它更简单
        async with asyncssh.connect(
            host=host,
            port=port,
            username=username,
            password='test123',
            known_hosts=None,
            client_keys=[]
        ) as conn:
            logger.info("✅ 连接成功！")
            
            # 尝试执行一个简单命令
            logger.info("执行测试命令...")
            result = await conn.run('echo "Hello SSH Gateway"', check=False)
            logger.info(f"命令执行结果: stdout='{result.stdout}', stderr='{result.stderr}', exit_status={result.exit_status}")

            if result.exit_status == 0:
                logger.info("✅ 命令执行成功！")
            else:
                logger.warning(f"⚠️ 命令执行失败，退出状态: {result.exit_status}")

            # 等待一段时间看看连接是否稳定
            logger.info("等待3秒钟测试连接稳定性...")
            await asyncio.sleep(3)

            logger.info("✅ 连接保持稳定！")
                
    except Exception as e:
        logger.error(f"❌ 连接失败: {e}")
        return False
    
    return True

async def main():
    """主函数"""
    logger.info("简单SSH连接测试")
    logger.info("=" * 40)
    
    success = await simple_ssh_test()
    
    if success:
        logger.info("🎉 测试成功！")
    else:
        logger.error("💥 测试失败！")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"程序错误: {e}")
