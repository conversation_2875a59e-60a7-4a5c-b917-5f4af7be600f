### **项目任务清单：远程设备SSH网关 (后端实现版)**

这是一个可以直接用于开发的后端代码结构。

#### **1. 项目结构**

首先，创建如下的目录和文件结构：

```
remote-ssh-gateway/
├── keys/
│   └── ssh_host_key      # (此文件需要您手动生成)
├── ssh_gateway/
│   ├── __init__.py       # (空文件)
│   ├── config.py         # 存放配置信息
│   ├── ws_manager.py     # WebSocket连接管理器
│   └── ssh_server.py     # 自定义SSH服务器
├── main.py               # 主应用启动器
└── requirements.txt      # Python依赖
```

---

#### **2. 文件内容**

将以下代码内容分别填入对应文件中。

##### **`requirements.txt`**

```txt
fastapi
uvicorn[standard]
asyncssh
```
*说明: `uvicorn[standard]` 包含 `websockets` 库，安装它更方便。*

**操作**：在终端运行 `pip install -r requirements.txt`。

---

##### **`ssh_gateway/config.py`**

```python
# ssh_gateway/config.py

# --- 服务器端口配置 ---
SSH_PORT = 8022
WEB_PORT = 8000
WEB_HOST = "0.0.0.0"

# --- SSH服务器配置 ---
# 您需要先生成这个密钥文件。命令: ssh-keygen -t rsa -f keys/ssh_host_key
SSH_HOST_KEY_PATH = "keys/ssh_host_key"

# --- 安全配置 ---
# 存放允许连接到此网关的Jumpserver的公钥
# 在Jumpserver系统用户中配置的私钥对应的公钥
# 这可以防止任意SSH客户端连接
ALLOWED_JUMPSERVER_PUBLIC_KEYS = [
    # 示例: "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIHlour/p52T0q2dSp5F4s3W..."
    # 请替换为您自己的Jumpserver公钥
]
```

---

##### **`ssh_gateway/ws_manager.py`**

```python
# ssh_gateway/ws_manager.py

import asyncio
from fastapi import WebSocket
import logging
from typing import Dict, Tuple

logging.basicConfig(level=logging.INFO)

class ConnectionManager:
    """
    单例模式的连接管理器，负责：
    1. 维护 device_id 到 WebSocket 的映射。
    2. 协调 SSH 会话和 WebSocket 之间的数据流。
    """
    def __init__(self):
        # 存储活动的WebSocket连接: {device_id: WebSocket}
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储等待结果的Future对象: {device_id: asyncio.Future}
        self.result_futures: Dict[str, asyncio.Future] = {}
        logging.info("ConnectionManager initialized.")

    async def connect(self, device_id: str, websocket: WebSocket):
        """接受新的WebSocket连接并存储。"""
        await websocket.accept()
        self.active_connections[device_id] = websocket
        logging.info(f"Device '{device_id}' connected via WebSocket.")

    def disconnect(self, device_id: str):
        """断开并清理指定device_id的连接。"""
        if device_id in self.active_connections:
            del self.active_connections[device_id]
        if device_id in self.result_futures:
            # 如果有正在等待的任务，让它异常退出
            future = self.result_futures.pop(device_id)
            future.set_exception(ConnectionError(f"Device '{device_id}' disconnected."))
        logging.info(f"Device '{device_id}' disconnected.")

    async def send_command(self, device_id: str, command_data: str) -> str:
        """
        通过WebSocket将命令发送给小程序，并异步等待结果。
        
        优化点：此函数现在处理原始数据流，而不仅仅是换行符分割的命令。
        """
        websocket = self.active_connections.get(device_id)
        if not websocket:
            logging.error(f"Cannot send command: No active WebSocket for device '{device_id}'.")
            raise ConnectionError(f"Device '{device_id}' is not connected.")

        # 为本次命令创建一个新的Future来等待结果
        future = asyncio.get_event_loop().create_future()
        self.result_futures[device_id] = future

        try:
            # 构造并发送JSON指令
            await websocket.send_json({"type": "input", "data": command_data})
            
            # 异步等待结果，设置一个超时时间防止无限等待
            result = await asyncio.wait_for(future, timeout=30.0)
            return result
        except asyncio.TimeoutError:
            logging.error(f"Timeout waiting for result from device '{device_id}'.")
            raise
        finally:
            # 清理本次命令的Future
            if device_id in self.result_futures and self.result_futures[device_id] is future:
                del self.result_futures[device_id]

    def on_result_received(self, device_id: str, result_data: str):
        """
        当从WebSocket收到结果时，调用此方法来完成对应的Future。
        """
        future = self.result_futures.get(device_id)
        if future and not future.done():
            future.set_result(result_data)
        else:
            logging.warning(f"Received result for '{device_id}', but no pending future found.")

# 创建一个全局唯一的管理器实例
manager = ConnectionManager()
```

---

##### **`ssh_gateway/ssh_server.py`**

```python
# ssh_gateway/ssh_server.py

import asyncssh
import logging
from .ws_manager import manager
from . import config

class MySSHServerSession(asyncssh.SSHServerSession):
    def __init__(self, device_id: str):
        self._device_id = device_id
        self._chan = None
        super().__init__()

    def connection_made(self, chan: asyncssh.SSHServerChannel):
        """当SSH会话通道建立时调用。"""
        self._chan = chan
        logging.info(f"SSH session created for device '{self._device_id}'.")

    def data_received(self, data: str, datatype: asyncssh.DataType):
        """
        接收来自Jumpserver终端的输入流。
        优化点：直接转发所有数据，以支持交互式应用(vim, top等)，而不仅仅是简单命令。
        """
        # 使用asyncio.create_task在后台发送命令并处理结果，避免阻塞data_received
        asyncio.create_task(self.run_command(data))

    async def run_command(self, command: str):
        """将命令通过WebSocket管理器发送，并等待结果写回SSH通道。"""
        try:
            logging.info(f"SSH -> WS ({self._device_id}): {command!r}")
            # 调用ws_manager发送命令并等待结果
            result = await manager.send_command(self._device_id, command)
            logging.info(f"WS -> SSH ({self._device_id}): {result!r}")
            # 将结果写回给Jumpserver的SSH通道
            self._chan.write(result)
        except ConnectionError as e:
            self._chan.write(f"\nError: {e}\n")
            self._chan.exit(1)
        except asyncio.TimeoutError:
            self._chan.write("\nError: Command timed out.\n")
        except Exception as e:
            logging.error(f"Error processing command for '{self._device_id}': {e}", exc_info=True)
            self._chan.write(f"\nAn internal error occurred: {e}\n")
            self._chan.exit(1)

    def connection_lost(self, exc: Exception | None) -> None:
        logging.info(f"SSH session for device '{self._device_id}' closed.")

class MySSHServer(asyncssh.SSHServer):
    def session_requested(self) -> MySSHServerSession:
        """当客户端请求会话时，创建我们的自定义会话实例。"""
        device_id = self.get_extra_info('username')
        return MySSHServerSession(device_id)

    def begin_auth(self, username: str) -> bool:
        """
        在密码或公钥认证前调用。
        可以在这里做一些初步的用户名检查。
        """
        # 简单地允许任何用户名，具体的路由逻辑在 session_requested 中处理
        return True # or False

    def public_key_auth_supported(self):
        """声明我们支持公钥认证。"""
        return True

    def validate_public_key(self, username: str, key: asyncssh.PublicKey) -> bool:
        """
        验证Jumpserver提供的公钥是否在我们允许的列表中。
        """
        key_str = key.export(format='openssh')
        logging.info(f"Attempting auth for user '{username}' with public key: {key_str}")
        
        if config.ALLOWED_JUMPSERVER_PUBLIC_KEYS and key_str not in config.ALLOWED_JUMPSERVER_PUBLIC_KEYS:
            logging.warning(f"Auth failed for '{username}': Public key not in allowed list.")
            return False
            
        logging.info(f"Public key for '{username}' validated successfully.")
        return True
```

---

##### **`main.py`**

```python
# main.py

import asyncio
import logging
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from ssh_gateway.ws_manager import manager
from ssh_gateway.ssh_server import MySSHServer
from ssh_gateway import config

# --- 日志配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)


# --- FastAPI 应用和 WebSocket 端点 ---
app = FastAPI()

@app.websocket("/ws/{device_id}")
async def websocket_endpoint(websocket: WebSocket, device_id: str):
    """
    WebSocket端点，用于小程序连接。
    """
    try:
        await manager.connect(device_id, websocket)
        while True:
            # 等待小程序发送执行结果
            data = await websocket.receive_json()
            log.info(f"Received from WebSocket ({device_id}): {data}")
            
            # 验证数据格式
            if data.get("type") == "output" and "data" in data:
                manager.on_result_received(device_id, data["data"])
            else:
                log.warning(f"Invalid data format from {device_id}: {data}")

    except WebSocketDisconnect:
        log.info(f"WebSocket for {device_id} disconnected.")
    except Exception as e:
        log.error(f"Error in WebSocket connection for {device_id}: {e}", exc_info=True)
    finally:
        manager.disconnect(device_id)


# --- 服务器启动逻辑 ---
async def start_ssh_server():
    """启动AsyncSSH服务器。"""
    log.info(f"Starting SSH server on port {config.SSH_PORT}...")
    try:
        await asyncssh.create_server(
            MySSHServer,
            host=config.WEB_HOST,
            port=config.SSH_PORT,
            server_host_keys=[config.SSH_HOST_KEY_PATH]
        )
    except Exception as e:
        log.error(f"Failed to start SSH server: {e}", exc_info=True)


async def start_web_server():
    """以编程方式启动Uvicorn服务器，使其与asyncssh在同一个事件循环中。"""
    log.info(f"Starting FastAPI/WebSocket server on http://{config.WEB_HOST}:{config.WEB_PORT}...")
    uv_config = uvicorn.Config(
        app, 
        host=config.WEB_HOST, 
        port=config.WEB_PORT,
        log_level="info"
    )
    server = uvicorn.Server(uv_config)
    # 我们不调用 server.run() 因为它会阻塞。
    # 我们调用 server.serve() 这是一个 awaitable。
    await server.serve()

async def main():
    """主函数，使用asyncio.gather并发运行SSH和Web服务器。"""
    log.info("Gateway starting up...")
    
    # 检查SSH密钥是否存在
    try:
        with open(config.SSH_HOST_KEY_PATH, 'r') as f:
            pass
    except FileNotFoundError:
        log.error("="*50)
        log.error(f"SSH host key not found at '{config.SSH_HOST_KEY_PATH}'!")
        log.error("Please generate it using: ssh-keygen -t rsa -f keys/ssh_host_key")
        log.error("="*50)
        return

    ssh_task = asyncio.create_task(start_ssh_server())
    web_task = asyncio.create_task(start_web_server())

    await asyncio.gather(web_task, ssh_task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log.info("Gateway shutting down.")

```

---

#### **3. 如何运行**

1.  **初始化环境**
    ```bash
    # 创建项目目录
    mkdir remote-ssh-gateway && cd remote-ssh-gateway
    
    # 创建Python虚拟环境
    python -m venv venv
    source venv/bin/activate  # on Windows: venv\Scripts\activate
    
    # 安装依赖
    pip install -r requirements.txt
    ```

2.  **生成SSH主机密钥**
    ```bash
    # 创建存放密钥的目录
    mkdir keys
    
    # 生成密钥文件 (按回车键接受默认设置，不要设置密码)
    ssh-keygen -t rsa -b 4096 -f keys/ssh_host_key
    ```

3.  **配置**
    -   编辑 `ssh_gateway/config.py`，将您的Jumpserver公钥添加到 `ALLOWED_JUMPSERVER_PUBLIC_KEYS` 列表中。

4.  **启动网关**
    ```bash
    python main.py
    ```
