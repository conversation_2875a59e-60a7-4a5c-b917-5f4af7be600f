# ssh_gateway/config.py

# --- 服务器端口配置 ---
SSH_PORT = 8022
WEB_PORT = 8002
WEB_HOST = "0.0.0.0"

# --- SSH服务器配置 ---
# 您需要先生成这个密钥文件。命令: ssh-keygen -t rsa -f keys/ssh_host_key
SSH_HOST_KEY_PATH = "keys/ssh_host_key"

# --- 安全配置 ---
# 存放允许连接到此网关的客户端公钥
# 注意：这里应该配置客户端认证用的公钥，而不是服务器主机密钥
# 这可以防止任意SSH客户端连接
ALLOWED_JUMPSERVER_PUBLIC_KEYS = [
    # 客户端认证密钥的公钥
    "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC6xcl9sxhly4gYb8JuV85pdTWbgEiWQXJOSpEF7PpwhyLwMMOArKN11wIZow81oG390gT6siB93+m05NQHM4/5gAq1bYUjHXzx4GNJm8a5gm0l7BE96Pi5I2+gAYAkq4Oy1jvnEvfuUdaIhrlFN+J0xHyble6u/gpj1wKN/s2RctKYyj4fC5AIVjDOwvHTs9f5rGiXGUWg3KAnMvOqgELMIVqusMULhoHE+ppgGN5WqL+FesHznNDydnub/xUJzLIUCW7cV1DqchowRUHhlUN4I/WBuNlKJggFBP2EWplDCJRff/2hRQkW8wWjqSg6HCohxFjt2Rd1ed8K+2WBwKKR chen@ChenBook"
]
